2023 年人工智能工程技术 职业技能竞赛













任







（竞赛时间：120 分钟）











工位号：_______________

第一部分 竞赛须知
一、竞赛要求
1、正确使用设备，操作安全规范；
2、竞赛过程中如有异议，可向现场考评人员反映，不得扰乱赛场秩序；
3、遵守赛场纪律，尊重考评人员，服从安排。
二、职业素养与安全意识
1、完成竞赛任务所有操作，符合安全操作规范，注意用电安全。
2、遵守赛场纪律，尊重赛场工作人员；爱惜赛场设备、器材。
三、扣分项
1、在完成竞赛过程中，因操作不当导致设备重大损坏或造成事故，视情节 扣 10～20 分，情况严重者取消比赛资格。
2、衣着不整、污染赛场环境、扰乱赛场秩序、干扰裁判工作等不符合职业 规范的行为，视情节扣 5～10 分，情节严重者取消竞赛资格。
四、选手须知
1、如出现缺页、字迹不清等问题，请及时向裁判示意，并进行更换；比赛 结束后，所有的竞赛相关材料不得带离赛场；
2、参赛团队应在规定时间内完成任务书要求的内容，任务实现过程中形成 的文件资料必须存储到指定位置；
3、比赛过程中，选手认定设备或器件有故障可向裁判员提出更换；如器件 或设备经测定完好属误判时，器件或设备的认定时间计入比赛时间；如果器件或 设备经测定确有故障，则当场更换设备，此过程中（设备测定开始到更换完成） 造成的时间损失，在比赛时间结束后，酌情对该小组进行等量的时间延迟补偿；
4、比赛过程中由于人为原因造成设备损坏，这种情况设备不予更换。
5、在裁判组宣布竞赛结束后，请选手立即停止竞赛中的任何操作。
五、注意事项

1、在每个.ipynb 文件作答完成之后，选手需要将其运行结果进行保存，保 存方法：选中对应的.ipynb 文件，按下Ctrl + s。Notebook 中程序的  输出结果请勿删除。

第二部分 竞赛任务

模块 C、综合工程技术应用

一、任务要求
根据项目要求完成人工智能综合工程技术应用代码开发，将“resource/ta sk/open_vocabulary_detection.ipynb”中每个步骤的输出结果和填写的代码 截图并粘贴在“resource/work/answer.doc”的指定位置中。最后按照《选手指 引》要求将上述文件保存至指定位置。
二、任务环境
l 硬件资源：高性能 GPU 计算机、场景验证平台、人工智能模型训练系统、 人机协同数据标注平台。
l 软件资源：“resource.zip”，pytorch 深度学习框架。
三、任务说明
1、开放词汇检测任务
在现实世界中，事物的种类是近乎无穷的。而为了保证任务的可行性， 通常  会给有限种类的物体进行标注用于训练检测模型。为了让模型进一步适应更为复  杂、包含无限物体种类的现实环境，设置一种开放词汇检测任务，即在测试时仅  通过（训练时）未见过样本的类别语义文本信息，即可对这些样本进行检测。开  放词汇检测模型可以利用预训练好的视觉-语言模型中的知识，建立类别语义文  本信息与未见过样本视觉特征的映射关系，进而实现对未见过样本的自适应检测， 提高智能模型多场景条件下的泛化能力，适应复杂、开放场景实际检测需求，对  生产实践具有重要意义。
进入人工智能模型训练系统，打开“task”文件夹，文件夹中提供了所需的 全部任务文件。请打开“task/open_vocabulary_detection.ipynb”文件，在该 文件内编写代码完成以下操作：
（1）步骤 1.图像数据和标签加载
部分代码已给出，请根据提示，将代码补充完整。
①请在<1>处补充代码，加载 COCO val2017 标注文件，使用 pycocotools.c

oco 中的接口输出所有 COCO 类别标签并将它们存储到数组 labels 中；
②请在<2>处补充代码定义函数，从数据集中加载一张图片，并使用 matplo tlib.pyplot 提供的接口按提示展示图片。
将该步骤全部输出结果进行截图并保存在“answer.doc”中，将<1><2>处补 充的代码截图并保存在“answer.doc”中。
（2）步骤 2.模型加载
部分代码已给出，请根据提示，将代码补充完整。
①请在<1>处补充代码，使用 clip 库提供的接口将预训练好的 CLIP 模型“V iT-B-32.pt”加载到 GPU 设备上，作为任务的预训练视觉-语言模型；
②请在<2>处补充代码，使用 sam_model_registry 函数将预训练好的 Segme nt Anything（SAM）模型加载到 GPU 设备上，作为任务的检测模型。
该步骤无输出结果，可在“answer.doc ”中填写“无”，将<1><2>处补充的 代码截图并保存在“answer.doc”中。
（3）步骤 3.标签文本特征生成
部分代码已给出，请根据提示，将代码补充完整。
①请在<1>处补充代码，使用 clip 库提供的接口将所有类别标签文本转换为 自然语言形式，然后进一步转化为 tokenized 的表示；
②请在<2>处补充代码，使用 CLIP 模型的 encode_text 函数对数据集的标签 文本进行编码，然后进行归一化。
将该步骤全部输出结果进行截图并保存在“answer.doc”中，将<1><2>处补 充的代码截图并保存在“answer.doc”中。
（4）步骤 4.图像分割掩码生成
部分代码已给出，请根据提示，将代码补充完整。
①请在<1>处补充代码，读取 image_id 为 1000 的本地图像；
②请在<2>处补充代码，调用 SAM 模型 mask generator 的 generate 方法， 对输入图像生成分割掩码（mask），计算生成的掩码数量。
将该步骤全部输出结果进行截图并保存在“answer.doc”中，将<1><2>处补 充的代码截图并保存在“answer.doc”中。
（5）步骤 5.开放词汇目标检测

部分代码已给出，请根据提示，将代码补充完整。
①请在<1>处补充代码，构造图像剪裁函数，生成的每个掩码的边界框为 xy wh 格式，使用该格式的边界框对图像进行裁剪；
②请在<2>处补充代码，构造标签预测函数，首先通过 CLIP 模型的 encode_ image 函数对裁剪后的图像进行特征提取，并归一化；
③请在<3>处补充代码，将生成的图像特征和步骤 3 中的标签文本特征计算 余弦相似度矩阵，将相似度输入 softmax 函数得到预测概率，选择预测概率最高 的标签类别作为预测标签；
④请在<4>处补充代码，计算预测标签对应的概率。
将该步骤全部输出结果进行截图并保存在“answer.doc”中，将<1>至<4>处 补充的代码截图并保存在“answer.doc”中。
（6）步骤 6.检测结果后处理
部分代码已给出，请根据提示，将代码补充完整。
①请在<1>处补充代码，使得预测结果存在重复边界框时，只保留预测概率 最大的一个；
②请在<2>处补充代码，去除掉预测结果中预测概率小于0.6 的边界框。
将该步骤全部输出结果进行截图并保存在“answer.doc”中，将<1><2>处补 充的代码截图并保存在“answer.doc”中。
（7）步骤 7.模型检测结果可视化
部分代码已给出，请根据提示，将代码补充完整。
①定义边界框显示函数。
②在左侧子图中展示图像的预测结果，包含预测目标的边界框和对应标签；
③在右侧子图中展示图像的真实标注信息，包含真实目标的边界框和对应标 签。
将该步骤全部输出结果进行截图并保存在“answer.doc”中，将<1>至<3>处 补充的代码截图并保存在“answer.doc”中。
（8）步骤 8. 计算检测指标
部分代码已给出，请根据提示，将代码补充完整。
①将模型的预测结果导出到 json 文件中；

②使用 pycocotools.cocoeval 的接口计算 mAP 检测指标。
将该步骤全部输出结果进行截图并保存在“answer.doc”中，将<1><2>处补 充的代码截图并保存在“answer.doc”中。
注：编写完成之后，需要将“answer.doc ”和“open_vocabulary_detection.i  pynb ”文件及其运行结果保存至“resource/work”文件夹。