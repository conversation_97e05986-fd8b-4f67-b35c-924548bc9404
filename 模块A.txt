2023 年人工智能工程技术 职业技能竞赛













任







（竞赛时间：210 分钟）











工位号：_______________

第一部分 竞赛须知
一、竞赛要求
1、正确使用设备，操作安全规范；
2、竞赛过程中如有异议，可向现场考评人员反映，不得扰乱赛场秩序；
3、遵守赛场纪律，尊重考评人员，服从安排。
二、职业素养与安全意识
1、完成竞赛任务所有操作，符合安全操作规范，注意用电安全。
2、遵守赛场纪律，尊重赛场工作人员；爱惜赛场设备、器材。
三、扣分项
1、在完成竞赛过程中，因操作不当导致设备重大损坏或造成事故，视情节 扣 10～20 分，情况严重者取消比赛资格。
2、衣着不整、污染赛场环境、扰乱赛场秩序、干扰裁判工作等不符合职业 规范的行为，视情节扣 5～10 分，情节严重者取消竞赛资格。
四、选手须知
1、如出现缺页、字迹不清等问题，请及时向裁判示意，并进行更换；比赛 结束后，所有的竞赛相关材料不得带离赛场；
2、参赛团队应在规定时间内完成任务书要求的内容，任务实现过程中形成 的文件资料必须存储到指定位置；
3、比赛过程中，选手认定设备或器件有故障可向裁判员提出更换；如器件 或设备经测定完好属误判时，器件或设备的认定时间计入比赛时间；如果器件或 设备经测定确有故障，则当场更换设备，此过程中（设备测定开始到更换完成） 造成的时间损失，在比赛时间结束后，酌情对该小组进行等量的时间延迟补偿；
4、比赛过程中由于人为原因造成设备损坏，这种情况设备不予更换。
5、在裁判组宣布竞赛结束后，请选手立即停止竞赛中的任何操作。

五、注意事项
1、在每个.ipynb 文件作答完成之后，选手需要将其运行结果进行保存，保 存方法：选中对应的.ipynb 文件，按下Ctrl + s。Notebook 中程序的  输出结果请勿删除。

第二部分 竞赛任务

模块 A、 自然语言处理技术应用

一、任务要求
根据项目要求完成人工智能自然语言处理技术应用代码开发，将“resource /task/sentence.ipynb”中每个步骤的输出结果和填写的代码截图并粘贴在“r esource/work/answer.doc”的指定位置中。最后按照《选手指引》要求将上述 文件及任务明确要求保存的模型保存至指定位置。
二、任务环境
l 硬件资源：高性能 GPU 计算机、场景验证平台、人工智能模型训练系统、 人机协同数据标注平台；
l 软件资源：“resource.zip ”, pytorch 深度学习框架。
三、任务说明
1、新闻文本分类
新闻文本分类根据内容将新闻文本归纳到特定的类别或主题，是自然语言处 理中的一项关键任务，可以对海量新闻进行高效的组织和筛选，帮助读者更快速 地找到感兴趣的信息。新闻文本通常包含丰富的信息，如事件、人物、地点、观 点等，而分类可以提供一个宏观的视角帮助用户了解文章的核心内容。通过高效 的新闻文本分类，可以增强信息的可访问性，提高用户的阅读体验，同时为新闻 机构和研究者提供有价值的数据分析。
进入人工智能模型训练系统，打开“task”文件夹。文件夹中提供了所需的 全部任务文件。请打开“task/sentence.ipynb”文件，在该文件内编写代码完 成以下操作：
（1）步骤 1.随机种子设置
部分代码已给出，请根据提示，将代码补充完整。
①请在<1>处补充代码，设置 python、numpy 和 pytorch 随机种子。
该步骤无输出结果，可在“answer.doc”中填写“无”，将<1>处补充的代

码截图并保存在“answer.doc”中。
（2）步骤 2.数据准备
部分代码已给出，请根据提示，将代码补充完整。
①请在<1>处补充代码，从“data/train.json ”中读取所有的文本数据和对 应的类别。
将该步骤全部输出结果进行截图并保存在“answer.doc”中，将<1>处补充 的代码截图并保存在“answer.doc”中。
（3）步骤 3.数据处理
部分代码已给出，请根据提示，将代码补充完整。
①请在<1>处补充代码，按字对句子切分，如果有大写字母则小写化，并且 需要删除空白字符。
将该步骤全部输出结果进行截图并保存在“answer.doc”中，将<1>处补充 的代码截图并保存在“answer.doc”中。
（4）步骤 4.模型结构定义
多头注意力（Multi-Head Attention）被广泛应用于 Transformer 架构，用 于捕捉序列数据中的长距离依赖关系。
部分代码已给出，请根据提示，将代码补充完整。
①定义 split_heads 函数，在<1>处补充代码，将输入的特征重新排列，并 对输入特征进行转置；
②请在<2>处补充代码，按照如下公式构造 scaled_dot_product_attention 函数，计算注意力权重。其中Q, K, V是输入经过线性层后的三个特征向量。

③请在<3>处补充代码，构造多头注意力模块的 forward 函数；
④构造新闻文本分类器，请在__init__函数<4>处补充代码，定义嵌入层、 LSTM 层、线性层和自注意力层(如果 use_attention==False 则使用 None)。
该步骤无输出结果，可在“answer.doc”中填写“无”，将<1>至<4>处补充 的代码截图并保存在“answer.doc”中。
（5）步骤 5.新闻文本分类器 forward 函数

部分代码已给出，请根据提示，将代码补充完整。
①请在<1>处补充代码，将输入的句子经过嵌入层、LSTM 层、注意力层（使 用残差连接）进行处理然后经过均值池化处理，最后通过线性层，再使用 nn.fu nctional 接口提供的对数 softmax 操作得到每个标签的概率分布并返回；
②请在<2>attn_output 处补充代码，根据变量 self.use_attention 判断是 否经过注意力层。
该步骤无输出结果，可在“answer.doc”中填写“无”，将<1><2>处补充的 代码截图并保存在“answer.doc”中。
（6）步骤 6.模型训练
标签平滑（Label Smoothing）是一种用于深度学习模型训练的正则化技术， 防止模型过拟合并提高泛化能力。
在有c个类别的分类问题中，对于每个样本，有一个硬标签向量y，其中 yi =1 表示目标类别，其余yj  = 0（其中j ≠ i）。设一个平滑参数E，其中0 < E < 1。
标签平滑将硬标签向量y转换为软标签向量yI ，向量中每个元素y的计算方法如
下：
                         
其中，k  表示类别索引，i  表示目标类别的索引。根据公式实现标签平滑。
部分代码已给出，请根据提示，将代码补充完整。
①请在<1>处补充代码，构造 label_smoothing 函数；
②请在<2>处补充代码，通过计算模型推理的概率分布，在交叉熵损失中引 入 label_smoothing 函数，从而构造 cross_entropy_loss_with_label_smoothi ng 函数；
③请在<3>处补充代码，循环遍历训练数据，并对模型进行训练。在每个训 练样本上进行前向计算，计算损失，并通过反向传播和优化器来更新模型的参数。 循环迭代多轮（由 NUM_EPOCHS 控制）。训练结束后运行下一代码块输出模型的 测试结果并将保存模型“model_before_kl.pt ”。
将该步骤全部输出结果进行截图并保存在“answer.doc”中，将<1>至<3>处 补充的代码截图并保存在“answer.doc”中。

（7）步骤 7.模型结果预测
部分代码已给出，请根据提示，将代码补充完整。
①请在<1>处补充代码，使用 prepare_sequence 函数进行映射；
②请在<2>处补充代码，在 label_probabilities 张量中，沿着 dim=1 维度 （标签维度）找到最大值和对应的索引；
③请在<3>处补充代码，根据 max_indices 中的索引，通过 ix_to_label 字 典将索引转换为相应的标签。
将该步骤全部输出结果进行截图并保存在“answer.doc”中，将<1>至<3>处 补充的代码截图并保存在“answer.doc”中。
（8）步骤 8.定义基于 LSTM 的学生编码器
定义一个基于 LSTM 的学生编码器，它可以利用多种不同的自然语言表征提 示（prompt），并通过池化处理来获得具有更好泛化能力的最终表征。
部分代码已给出，请根据提示，将代码补充完整。
①请在<1>处补充代码，使用prompt 生成组装好的句子列表（list），每一 个类别都需要用到全部 prompt，并转换成对应的 id；将该列表输入作为输入， 经过嵌入层和 LSTM 层得到句子的输出向量；
②请在<2>处补充代码，计算句子输出向量在长度上的平均表示（mean rep resentation）；
③请在<3>处补充代码，使用 cat 函数把 list 转换为 tensor；
④请在<4>处补充代码，计算句子在模板上的平均表示（mean prompt repr esentation）。
该步骤无输出结果，可在“answer.doc”中填写“无”，将<1>至<4>处补充 的代码截图并保存在“answer.doc”中。
（9）步骤 9.定义基于 BERT 的教师编码器模型
部分代码已给出，请根据提示，将代码补充完整。
①请在<1>处补充代码，使用 prompt 生成组装好的句子 list，每一个类别 都需要应用到全部 prompt；
②请在<2>处补充代码，计算句子在序列长度上的平均表示（mean represe ntation）；

③请在<3>处补充代码,定义均方误差损失函数（MSE），用于增大学生模型  提取到的特征和教师模型提取到的特征之间的匹配程度，以便学习到更好的特征；
④请在<4>处补充代码，训练模型并将训练好的模型保存为“model_after_ kl.pth ”。
将该步骤全部输出结果进行截图并保存在“answer.doc”中，将<1>至<4>处 补充的代码截图并保存在“answer.doc”中。
注 1： 编写完成后，需要将“model_before_kl.pt”和“model_after_kl.  pth”模型、“answer.doc ”和“sentence.ipynb ”文件及其运行结果保存至“r  esource/work”文件夹。
注 2：对抗安全评测任务需要继续在“sentence.ipynb”中编写代码，并执 行接下来的步骤，保存运行结果。
2、对抗安全评测
对抗样本的出现对人工智能的安全使用带来了极大的挑战, 尤其是在自动 驾驶、舆情分析等安全敏感的领域，对抗样本的出现带来了更多不确定性，削弱 了人工智能的赋能效果。对抗样本(adversarial examples)是指在自然样本上 添加微小的扰动而形成的样本, 相较于自然样本, 此类样本的改变通常不影响 人类的肉眼判断, 但可以使模型以高置信度得出与自然样本不同的输出。为了抑 制对抗样本的负面影响，对模型的鲁棒性进行测评成为保障模型安全落地应用的 重要环节。
（1）步骤 1.模型鲁棒性评测
FGSM 算法是对抗样本攻击中的经典算法之一。FGSM 方法的主要思想是在输 入数据的基础上，添加损失函数梯度方向的扰动，从而生成对抗样本。这个扰动 的大小由ε超参数决定，控制了扰动的幅度。对于输入样本x，损失函数J，模型的 梯度∇J(x, y)（其中y是真实标签），FGSM 生成的对抗样本xadv 的计算步骤为：
1、计算损失函数关于输入样本的梯度：grad = ∇J(x, y) ;
2、计算对抗样本：xadv  = x + ε * sign(grad) ;
部分代码已给出，请根据提示，将代码补充完整。
①请在<1>处填写你的 USER_ID;
(请勿在“answer.doc ”中记录截取你填写的 USER_ID，若 USER_ID 在“an

swer.doc ”中出现则视为作弊处理!)
②请在<2>处补充代码，定义 model_predict 函数；
③请在<3>处补充代码,读取 json 文件中的对抗样本数据，并将其中的adv_ text 提取出来，逐个存入 src_texts 中；
④请在<4>处补充代码，对生成的对抗样本数据集进行模型推理，保存推理 结果至 src_label 中；
⑤请在<5>处补充代码，读取模型鲁棒性评测接口，将推理结果作为输入， 调用鲁棒性评测工具，返回模型的准确率，准确率越高，模型鲁棒性越强。
将该步骤全部输出结果进行截图并保存在“answer.doc”中，将<1>至<5>处 补充的代码截图并保存在“answer.doc”中。
（2）步骤 2.模型鲁棒性加固
通过对抗训练增强文本分类模型抵抗对抗样本的能力，提高智能模型的鲁棒 性。通过在训练样本中添加一定比例的对抗样本，模型将在训练中学习到对抗样 本的知识，提升将对抗样本正确分类的能力，从而提升模型的鲁棒性。
部分代码已给出，请根据提示，将代码补充完整。
①请在<1>处补充代码定义对抗模块，在训练样本的嵌入特征（embedding） 上添加随机扰动。根据公式补全扰动网络嵌入特征（embedding）值的代码：
rat  = r + ε * sign(grad)
其中grad是 param 的属性之一，sign(grad)函数可以使用grad除以grad的范数进 行计算。补全恢复网络原嵌入特征（embedding）值的代码，该值为 FGSM_embed ding 的 attack 函数中储存的中间变量。
②请根据提示在<2>处补充使用对抗模块进行扰动的代码，观察训练中 loss 的变化，并保存加固后的模型“model_adv.pt ”。
将该步骤全部输出结果进行截图并保存在“answer.doc”中，将<1>至<2>处 补充的代码截图并保存在“answer.doc”中。
（3）步骤 3.加固模型鲁棒性测试
部分代码已给出，请根据提示，将代码补充完整。
①请在<1>处补充代码读取对抗样本数据；
②请在<2>处补充代码，使用鲁棒性加固后的模型，对生成的对抗样本数据

集进行模型推理，保存推理结果；
③请在<3>处补充代码，读取模型鲁棒性评测接口，将新的推理结果 src_la bel 作为输入，调用鲁棒性评测工具，返回鲁棒性加固后的模型的准确率，观察 加固前后模型鲁棒性的变化。
将该步骤全部输出结果进行截图并保存在“answer.doc”中，将<1>至<3>处 补充的代码截图并保存在“answer.doc”中。
注：编写完成后，将“model_adv.pt”模型、“answer.doc”和“sentenc e.ipynb”文件及其运行结果保存至“resource/work”文件夹。