2023 年人工智能工程技术 职业技能竞赛













任







（竞赛时间：210 分钟）











工位号：_______________

第一部分 竞赛须知
一、竞赛要求
1、正确使用设备，操作安全规范；
2、竞赛过程中如有异议，可向现场考评人员反映，不得扰乱赛场秩序；
3、遵守赛场纪律，尊重考评人员，服从安排。
二、职业素养与安全意识
1、完成竞赛任务所有操作，符合安全操作规范，注意用电安全。
2、遵守赛场纪律，尊重赛场工作人员；爱惜赛场设备、器材。
三、扣分项
1、在完成竞赛过程中，因操作不当导致设备重大损坏或造成事故，视情节 扣 10～20 分，情况严重者取消比赛资格。
2、衣着不整、污染赛场环境、扰乱赛场秩序、干扰裁判工作等不符合职业 规范的行为，视情节扣 5～10 分，情节严重者取消竞赛资格。
四、选手须知
1、如出现缺页、字迹不清等问题，请及时向裁判示意，并进行更换；比赛 结束后，所有的竞赛相关材料不得带离赛场；
2、参赛团队应在规定时间内完成任务书要求的内容，任务实现过程中形成 的文件资料必须存储到指定位置；
3、比赛过程中，选手认定设备或器件有故障可向裁判员提出更换；如器件 或设备经测定完好属误判时，器件或设备的认定时间计入比赛时间；如果器件或 设备经测定确有故障，则当场更换设备，此过程中（设备测定开始到更换完成） 造成的时间损失，在比赛时间结束后，酌情对该小组进行等量的时间延迟补偿；
4、比赛过程中由于人为原因造成设备损坏，这种情况设备不予更换。
5、在裁判组宣布竞赛结束后，请选手立即停止竞赛中的任何操作。
五、注意事项

1、在每个.ipynb 文件作答完成之后，选手需要将其运行结果进行保存，保 存方法：选中对应的.ipynb 文件，按下Ctrl + s。Notebook 中程序的  输出结果请勿删除。

第二部分 竞赛任务

模块 B、计算机视觉技术应用

一、任务要求
根据项目要求完成人工智能计算机视觉技术应用代码开发，将“resource/   task/t1/classification.ipynb ”和“resource/task/t2/detection.ipynb ”中   每个步骤的输出结果和填写的代码截图并粘贴在“resource/work/answer.doc ” 的指定位置中。最后按照《选手指引》要求将上述三个文件及任务明确要求保存   的模型保存至指定位置。
二、任务环境
l 硬件资源：高性能 GPU 计算机、场景验证平台、人工智能模型训练系统、 人机协同数据标注平台；
l 软件资源：“resource.zip”，pytorch 深度学习框架；
三、任务说明
1、非均衡样本分类任务
在现实世界，环境中不同类别样本的分布非常不均衡，一般由大量的常见样 本和少量的特殊样本构成。例如在安检任务中，需要被检出违禁品是极少出现的， 存在违禁品和非违禁品之间的分布不均。因此研究在样本不均衡数据上的提高分 类任务的准确性，对生产实践具有重要意义。
进入人工智能模型训练系统，打开“task”文件夹，文件夹中提供了所需的 全部任务文件。请打开“task/t1/classification.ipynb”文件，在该文件内编 写代码完成以下操作：
（1）步骤 1.图像数据预处理定义
部分代码已给出，请根据提示，将代码补充完整。
①请在<1>处补充代码，使用 torchvision.transforms 中提供的接口实现以 下操作：定义训练集和测试集数据预处理操作，将数据大小重置为(224, 224)， 并转换为 tensor 数据，对数据进行归一化处理，将各通道的均值和标准差均设 置为 0.5，最后使用 torchvision.transforms.Compose 整合以上预处理操作。

②为减轻数据分布不均衡的影响，请在<2>处补充代码，使用 torchvision. transforms 中提供的接口对数据进行增强：剪裁数据中心点周围（144,144）大 小区域，对数据进行水平翻转，对数据进行 30 度的随机旋转，并进行和①相同 的预处理操作。
③除了以上基础的方法，数据增强的方式也在不断的推陈出新，例如 mixup 方法，会通过混合数据及其标签来在分布不均衡的数据集上提高模型的泛化性。 请根据下面给出的 mixup 主要公式，结合代码中给出的提示，在<3>处补充代码 实现 mixup 数据增强函数的构造。
 = λxi  + (1 - λ)xj
 = λyi  + (1 - λ)yj
其中xi 和xj 为原始输入向量，yi 和yj 为标签的 one-hot 编码。
将该步骤全部输出结果进行截图并保存在“answer.doc”中，将<1><2><3> 处补充的代码截图并保存在“answer.doc”中。
（2）步骤 2.图像数据预处理
部分代码已给出，请根据提示，将代码补充完整。
①请在<1>处补充代码，使用 torchvision.datasets 中的接口读取训练集和 测试集的数据，同时用定义好的预处理操作对数据进行预处理。
②请在<2>处补充代码，使用 torchvision.datasets 中的接口先对训练集数 据进行增强，再将增强后的数据加入到训练集中扩充训练集，更新训练集标签。
将该步骤全部输出结果进行截图并保存在“answer.doc”中，将<1><2>处补 充的代码截图并保存在“answer.doc”中。
（3）步骤 3.可视化图像数据获取及标注
请根据《平台与设备使用指南》操作设备获取图像并进行标注。
①将场景验证平台接入模型训练系统，通过平台的摄像头分别获取猫、狗道  具模型各角度（前后左右和俯视）图像数据 5 张，将获取的图像数据保存到“t  ask/t1/data/vis”文件夹中，同时将获取的猫狗模型图像，在“answer.doc ” 中各保存一张；
②使用标注平台对①中获取的图像数据进行标注，类别标签设置为“cat ”

和“dog”两种，仅需使用这两种标签对图像进行标注。操作过程中仅需截图保 存一张图像的标注界面，将标注好的全部数据导出为 CSV 格式，将导出数据保存 到“task/t1/data/labels ”文件夹中，截图展示该文件夹中的内容。
将上述过程中的截图保存在“answer.doc ”中。
（4）步骤 4.分类模型设置
部分代码已给出，请根据提示，将代码补充完整。
①请在<1>处补充代码，使用未经预训练的 resnet18 作为分类预测模型，需 使用 torchvision.models 接口调用 resnet18；
②请在<2>处补充代码，设置模型全连接层的输出维度为类别数；
③请在<3>处补充代码，将交叉熵损失作为模型的损失函数；
④在面对非均衡样本时，可以有效地均衡样本数量带给模型的负面影响，请 根据如下公式，结合上下文在<4>处补充代码，实现该损失函数。
FL(pt ) = 一(1 一 pt )ylogpt

⑤请在<5>处补充代码，使用 focal 损失替换③中的交叉熵损失函数；
⑥请在<6>处补充代码，选择 Adam 作为模型优化器。
将该步骤全部输出结果进行截图并保存在“answer.doc”中，将<1>至<6>处 补充的代码截图并保存在“answer.doc”中。
（5）步骤 5.模型训练和验证函数构造
部分代码已给出，请根据代码提示，将代码补充完成。
①请在<1>处补充代码构造模型训练函数，结合 mixup 策略构造最终的损失 函数计算公式，计算损失函数并进行梯度反传；
②请在<2>处补充代码，实现每 200 个 iteration 输出当前的训练进度（当 前 iteration/总 iteration）和损失，并在每个 epoch 结束后计算该 epoch 的平 均损失；
③请在<3>处补充代码，计算模型预测的准确率。
将该步骤全部输出结果进行截图并保存在“answer.doc”中，将<1>至<3>处 补充的代码截图并保存在“answer.doc”中。

（6）步骤 6.模型测试结果可视化
部分代码已给出，请根据代码提示，将代码补充完成。
①请在<1>处补充代码，读取保存好的模型 model_resnet18.pth，读取“ta sk/t1/data/vis”文件夹下的样本；
②请在<2>处补充代码，预测分类结果并进行可视化，展示待测样本的分类 结果。
将该步骤全部输出结果进行截图并保存在“answer.doc”中，将<1><2>处补 充的代码截图并保存在“answer.doc”中。
（7）步骤 7.测试样本特征获取
部分代码已给出，请根据代码提示，将代码补充完成。
①请在<1>处补充代码定义 Identity 函数，用于获取分类层的输入特征，并 将该特征作为样本视觉特征。
该步骤无输出结果，可在“answer.doc”中填写“无”，将<1>处补充的代 码截图并保存在“answer.doc”中。
（8）步骤 8.模型分类结果可视化
部分代码已给出，请根据代码提示，将代码补充完成。
①请在<1>处补充代码，读取保存好的模型 model_resnet18.pth，加载测试 目录 data/test 下的待测试样本；
②请在<2>处补充代码，对测试样本进行预处理，处理方式同步骤 1；
③请在<3>处补充代码加载模型model_resnet18.pth，预测测试集分类结果；
④请在<4>处补充代码，使用 Identity 函数修改③加载的模型结构，获取测 试样本特征；
⑤请在<5>处补充代码，根据测试集特征绘制平面 t-SNE 散点图，要求使用 pca 降维，‘cat’和‘dog’测试样本的分类结果使用不同颜色的散点表示。
将该步骤全部输出结果进行截图并保存在“answer.doc”中，将<1>-<5>处 补充的代码截图并保存在“answer.doc”中。
注：编写完成之后，需要将“model_resnet18.pth ”模型、“answer.doc ” 和“classification.ipynb ”文件及其运行结果保存至“resource/work”文件   夹。

2、目标检测任务
目标检测是计算机视觉领域的核心问题之一，其目的是找出图像中所有感兴 趣的目标，确定其位置和大小并识别出具体类别。目标检测广泛应用于机器人导 航、智能视频监控、工业检测、遥感监测等领域， 具有重要的现实意义。本任务 将对基于 Transformer 的目标检测模型 DETR 进行微调，使之可以在Wheat Dete ction Dataset 上执行小麦目标检测任务。
进入人工智能模型训练系统，打开“task”文件夹，文件夹中提供了所需的 全部任务文件。请打开“task/t2/detection.ipynb”文件，在该文件内编写代 码完成以下操作：
（1）步骤 1.数据预处理
部分代码已给出，请根据提示，将代码补充完整。
①请在<1>处补充代码，使用 pandas 库读取“./train.csv ”件，并在数据 集中新增 x、y、w、h 四列，将原 bbox 列中的四个数字进行拆分并转换为数值形 式，按顺序分别保存在x、y、w、h 四个列中。
②请在<2>处补充代码，在 df_image_ids 中增加一个 fold 字段，将整个数 据集随机划分为 5 等份，每份数据集的 fold 编号依次为 0-4，用于 k 折交叉验 证实验。
③请在<3>处补充代码，定义数据增强操作：使用 albumentations 库对训练 集数据进行随机水平翻转和随机垂直翻转（翻转概率均为 0.5），图像大小重新 调整为 512*512。
将该步骤全部输出结果进行截图并保存在“answer.doc”中，将<1>至<3>处 补充的代码截图并保存在“answer.doc”中。
（2）步骤 2.数据集和数据加载设置
部分代码已给出，请根据提示，将代码补充完整。
①请在<1>处补充代码，根据步骤 1 中划分好的数据集，补全 WheatDataset 数据集类的__len__方法；
②请在<2>处补充代码，根据步骤 1 中划分好的数据集，补全 WheatDataset 数据集类的__getitem__方法。
该步骤无输出结果，可在“answer.doc”中填写“无”，将<1><2>处补充的

代码截图并保存在“answer.doc”中。
（3）步骤 3.模型构建
部分代码已给出，请根据提示，将代码补充完整。
①请在<1>处补充代码，加载已经训练好的 detr_resnet50 模型；
②请在<2>处补充代码，修改 detr_resnet50 模型最后的 class_embed 线性 层，将其输入维度修改为 in_features，输出维度修改为 num_classes；
③请在<3>处补充代码，实现在 forward 方法中直接调用 detr_resnet50 模 型对输入图片进行目标检测并返回检测结果。
将该步骤全部输出结果进行截图并保存在“answer.doc”中，将<1>至<3>处 补充的代码截图并保存在“answer.doc”中。
（4）步骤 4. 实现 GIoU 计算函数
根据给出的 IoU 计算函数 box_iou，按照如下提示及示意图补全 GIoU 计算 函数 generalized_box_iou。
①请在<1>处补充代码，进行边界框坐标转换：generalized_box_iou 函数 的输入参数中的边界框的形式为 xywh 形式，先将其转化为 x1y1x2y2（矩形框左 上和右下坐标点）形式。
②请在<2>处补充代码，实现 GIoU 计算过程：对于任意的两个边界框A、 B， 首先找到一个能够包住它们的最小框C , 然后计算 C \ (A ∪ B)  的面积与C 的面积的比值，再用A、B的 IoU 值减去这个比值即可得到 GIoU。（注：C \ (A ∪ B) 的面积为 C 的面积减去 A∪B 的面积）

将该步骤全部输出结果进行截图并保存在“answer.doc”中，将<1><2>处补 充的代码截图并保存在“answer.doc”中。
（5）步骤 5. 补全匈牙利匹配的计算过程
部分代码已给出，请根据提示，将代码补充完整。

①请在<1>处补充代码，计算输出边界框和目标边界框之间的 L1 cost；
②请在<2>处补充代码，计算输出边界框和目标边界框之间的 GIoU cost；
③请在<3>处补充代码，将 classification cost、L1 cost、GIoU cost 按 照 HungarianMatcher 的__init__方法中指定的权重相加，得到最终的 cost 矩 阵。
将该步骤全部输出结果进行截图并保存在“answer.doc”中，将<1>至<3>处 补充的代码截图并保存在“answer.doc”中。
（6）步骤 6. 模型训练和验证函数
部分代码已给出，请根据提示，将代码补充完整。
①根据给出的代码提示，请在<1>处补全训练函数 train_fn；
②根据给出的代码提示，请在<2>处补全测试函数 eval_fn。
该步骤无输出结果，可在“answer.doc”中填写“无”，将<1><2>处补充的 代码截图并保存在“answer.doc”中。
（7）步骤 7. 实现完整的训练过程
部分代码已给出，请根据提示，将 run 函数补充完整。
①根据提供的 fold_num 参数，结合数据集中添加的 fold 列，请在<1>处补 全代码，划分训练集和测试集；
②请在<2>处补充代码，分别定义训练集和测试集的 DataLoader；
③请在<3>处补充代码，选择 AdamW 作为模型优化器；
④请在<4>处补充代码，实现在训练过程中保存模型在测试集上的平均损失 最小的模型，模型命名为 detr_best_ {fold_num}.pth。
该步骤无输出结果，可在“answer.doc”中填写“无”，将<1>至<4>处补充 的代码截图并保存在“answer.doc”中。
（8）步骤 8. 目标检测模型泛化性能评估
部分代码已给出，请根据提示，将代码补充完整。
①请在<1>处补充代码，使用 k 折交叉验证评估模型泛化性能：将 fold_num 依次设置为 0～4，执行 run 函数完成 5 次完整的训练过程；
②请在<2>处补充代码，输出 k 个模型在测试集上损失的均值和标准差，作 为模型泛化性能评估得分。

（9）步骤 9.检测结果可视化
部分代码已给出，请根据提示，将代码补充完整。
①请在<1>处补充代码，将步骤 8 中训练好的 detr_best_2.pth 模型加载到 内存中；
②请在<2>处补全充代码，将 fold 编号为 2 的数据集作为测试集；
③请在<3>处补充代码，定义测试集的 DataLoader，读取测试集上的第一张 图片；
④请在<4>处补充代码，在原图像上使用两种不同的颜色画出模型输出的检 测框（预测概率大于 0.5）和对应的真实标签（ groundtruth）。
将该步骤全部输出结果进行截图并保存在“answer.doc”中，将<1>至<3>处 补充的代码截图并保存在“answer.doc”中。
注：编写完成之后，需要将“detr_best_0.pth ”、“detr_best_1.pth ”、  “detr_best_2.pth ”、“detr_best_3.pth ”、“detr_best_4.pth ”模型、“a   nswer.doc”和“detection.ipynb”文件及其运行结果保存至“resource/work ”  文件夹。